#!/bin/bash
# Usage: ./deploy.sh [environment] [github_username] [github_access_token]

set -e  # Exit immediately if a command exits with a non-zero status

# Get parameters from command line or use defaults
ENVIRONMENT=${1:-staging}
GIT_USERNAME=${2:-""}
GIT_ACCESS_TOKEN=${3:-""}

# Set branch based on environment
if [[ "$ENVIRONMENT" == "production" ]]; then
  BRANCH="main"
elif [[ "$ENVIRONMENT" == "staging" ]]; then
  BRANCH="staging"
else
  BRANCH="development"
fi

echo "======================================================"
echo "👨🏻‍💻 Deploying to $ENVIRONMENT environment from $BRANCH branch... 🚀"
echo "======================================================"


# Pull the latest code if we're in a git repository
echo "======================================================"
echo "📍 STEP 1: SOURCE CODE UPDATE"
echo "======================================================"
echo "📍 Pulling latest code from $BRANCH branch..."
git pull origin $BRANCH --no-rebase || { echo "💢 Error ❌: git pull failed"; exit 1; }

# Update permissions for Laravel directories
echo "======================================================"
echo "📍 STEP 2: PERMISSIONS UPDATE"
echo "======================================================"
# Set ownership for storage and bootstrap/cache directories
sudo chown -R www-data:www-data storage bootstrap/cache

# Set proper permissions for directories and files
echo "  ➡️ Setting directory permissions to 777..."
sudo find storage bootstrap/cache -type d -exec chmod 777 {} \;
echo "  ➡️ Setting file permissions to 664..."
sudo find storage bootstrap/cache -type f -exec chmod 664 {} \;

# Install dependencies
echo "======================================================"
echo "📍 STEP 3: DEPENDENCIES INSTALLATION"
echo "======================================================"
export COMPOSER_ALLOW_SUPERUSER=1
composer install --no-dev --no-interaction --prefer-dist || { echo "💢 Error ❌: composer install failed"; exit 1; }


# Build and publish Mixpost assets
echo "======================================================"
echo "📍 STEP 4: LANGUAGE FILES PROCESSING"
echo "======================================================"
php artisan mixpost:convert-lang-json

echo "======================================================"
echo "📍 STEP 5: GW SOCIAL ASSET BUILDING"
echo "======================================================"
cd packages/inovector/mixpost-pro-team/ && \
npm run build && \
cd ../../../ && \
php artisan mixpost:publish-assets --force=true || { echo "💢 Error ❌: Mixpost asset build failed"; exit 1; }

echo "======================================================"
echo "📍 STEP 6: STORAGE LINK CREATION"
echo "======================================================"
php artisan storage:link --force || { echo "💢 Error ❌: Failed to create storage link"; exit 1; }


echo "======================================================"
echo "📍 STEP 7: APPLICATION OPTIMIZATION"
echo "======================================================"
# Optimize application and run migrations
echo "📍 Finalizing deployment..."

# Clear optimization cache
echo "  ➡️ Clearing optimization cache..."
php artisan optimize:clear || { echo "💢 Error ❌: Failed to clear optimization cache"; exit 1; }

# Run database migrations
echo "  ➡️ Running database migrations..."
php artisan migrate --force || { echo "💢 Error ❌: Database migration failed"; exit 1; }

# Cache configuration
echo "  ➡️ Caching configuration..."
php artisan config:cache || { echo "💢 Error ❌: Configuration cache failed"; exit 1; }

# Cache routes
echo "  ➡️ Caching routes..."
php artisan route:cache || { echo "💢 Error ❌: Route cache failed"; exit 1; }

# Cache views
echo "  ➡️ Caching views..."
php artisan view:cache || { echo "💢 Error ❌: View cache failed"; exit 1; }

# Final optimization
echo "  ➡️ Running final optimization..."
php artisan optimize || { echo "💢 Error ❌: Final optimization failed"; exit 1; }
