<?php

/**
 * Debug script to identify media path resolution issues
 * Run this with: php debug-media-paths.php
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Storage;
use Inovector\Mixpost\Models\Media;

echo "🔍 GravityWrite Social Media Path Debug\n";
echo "=====================================\n\n";

// 1. Check environment configuration
echo "📍 Environment Configuration:\n";
echo "APP_URL: " . config('app.url') . "\n";
echo "APP_ENV: " . config('app.env') . "\n";
echo "MIXPOST_DISK: " . config('mixpost.disk') . "\n";
echo "FILESYSTEM_DISK: " . config('filesystems.default') . "\n\n";

// 2. Check filesystem configuration
echo "📍 Filesystem Configuration:\n";
$mixpostDisk = config('mixpost.disk', 'public');
echo "Mixpost Disk: " . $mixpostDisk . "\n";

$diskConfig = config("filesystems.disks.{$mixpostDisk}");
if ($diskConfig) {
    echo "Disk Driver: " . ($diskConfig['driver'] ?? 'N/A') . "\n";
    echo "Disk Root: " . ($diskConfig['root'] ?? 'N/A') . "\n";
    echo "Disk URL: " . ($diskConfig['url'] ?? 'N/A') . "\n";
} else {
    echo "❌ Disk configuration not found!\n";
}

// 3. Test storage path resolution
echo "\n📍 Storage Path Resolution:\n";
try {
    $storage = Storage::disk($mixpostDisk);
    echo "Storage Root Path: " . $storage->path('') . "\n";
    echo "Test File Path: " . $storage->path('test.jpg') . "\n";
    echo "Test File URL: " . $storage->url('test.jpg') . "\n";
} catch (Exception $e) {
    echo "❌ Error testing storage: " . $e->getMessage() . "\n";
}

// 4. Check recent media records
echo "\n📍 Recent Media Records:\n";
try {
    $recentMedia = Media::latest()->take(3)->get();
    
    if ($recentMedia->count() > 0) {
        foreach ($recentMedia as $media) {
            echo "Media ID: " . $media->id . "\n";
            echo "  Disk: " . $media->disk . "\n";
            echo "  Path: " . $media->path . "\n";
            echo "  Full Path: " . $media->getFullPath() . "\n";
            echo "  URL: " . $media->getUrl() . "\n";
            echo "  Is Local Adapter: " . ($media->isLocalAdapter() ? 'Yes' : 'No') . "\n";
            echo "  File Exists: " . (file_exists($media->getFullPath()) ? 'Yes' : 'No') . "\n";
            echo "  ---\n";
        }
    } else {
        echo "No media records found.\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking media: " . $e->getMessage() . "\n";
}

// 5. Check for hardcoded paths
echo "\n📍 System Path Information:\n";
echo "Base Path: " . base_path() . "\n";
echo "Storage Path: " . storage_path() . "\n";
echo "Public Path: " . public_path() . "\n";
echo "Current Working Directory: " . getcwd() . "\n";

echo "\n✅ Debug completed!\n";
