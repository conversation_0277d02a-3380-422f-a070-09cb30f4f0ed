<?php

if (!function_exists('wordCount')) {
    /**
     * Counts the number of words in the given text.
     *
     * This function is a proxy for preg_split, and the word count is determined by
     * splitting the text on whitespace characters and counting the resulting array.
     *
     * @param string $text
     *
     * @return int
     */
    function wordCount($text)
    {
        $cleanedStr = str_replace(['#', '|'], ' ', $text);

        return count(preg_split('/\s+/', $cleanedStr));
    }
}

if (! function_exists('joinPath')) {
    /**
     * Join the given paths together.
     *
     * @param  string|null  $basePath
     * @param  string  ...$paths
     * @return string
     */
    function joinPath($basePath, ...$paths)
    {
        foreach ($paths as $index => $path) {
            if (empty($path)) {
                unset($paths[$index]);
            } else {
                $paths[$index] = DIRECTORY_SEPARATOR . ltrim($path, DIRECTORY_SEPARATOR);
            }
        }

        return $basePath . implode('', $paths);
    }
}

if (!function_exists('feUrl')) {
    /**
     * Creates a full URL for a frontend path.
     *
     * @param string $path
     *
     * @return string
     */
    function feUrl(string $path)
    {
        return joinPath(config('app.fe_url'), $path);
    }
}
