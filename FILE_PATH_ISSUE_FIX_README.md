# GravityWrite Social Media - File Path Issue Fix

## Problem Description

When clicking "Post Now" for posts containing images, the system encounters this error:
```
file_get_contents(/home/<USER>/clone-social.gravitywrite.com/storage/app/public/...): Failed to open stream: No such file or directory
```

**Key Issues:**
1. System looking for files in `/home/<USER>/clone-social.gravitywrite.com/` instead of `/home/<USER>/social.gravitywrite.com/`
2. TwitterOAuth library calling `file_get_contents()` with incorrect paths
3. Potential cached configuration or environment mismatch

## Root Cause Analysis

The issue occurs in the TwitterOAuth library at:
- **File**: `vendor/inovector/twitteroauth/src/TwitterOAuth.php`
- **Line**: 382
- **Method**: `uploadMediaNotChunked()`
- **Code**: `file_get_contents($parameters['media'])`

The path resolution chain:
1. `ManagesResources::uploadMedia()` calls `$item->getFullPath()`
2. `Media::getFullPath()` calls `$this->filesystem()->path($this->path)`
3. <PERSON><PERSON>'s filesystem resolves the path incorrectly

## Solution Steps

### 1. Run Diagnostic Scripts

First, run the diagnostic script to identify the exact issue:

```bash
# Make scripts executable
chmod +x fix-file-path-issue.sh

# Run the comprehensive fix script
./fix-file-path-issue.sh

# Run detailed path analysis
php debug-media-paths.php
```

### 2. Clear All Caches

```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
php artisan optimize:clear
```

### 3. Check Production Environment

Verify your production `.env` file contains:
```env
APP_URL=https://social.gravitywrite.com
MIXPOST_DISK=spaces  # or your correct disk
```

### 4. Fix Database Records (if needed)

If media records contain incorrect paths:

```bash
php fix-media-paths.php
```

### 5. Apply Code Fix

The TwitterProvider has been updated to use `ManagesResourcesFixed` which includes:
- Better error handling for file paths
- Automatic path correction for domain issues
- Fallback to URL-based uploads when local files fail
- Comprehensive logging for debugging

## Files Modified

1. **`packages/inovector/mixpost-pro-team/src/SocialProviders/Twitter/Concerns/ManagesResourcesFixed.php`**
   - Enhanced version of ManagesResources with path fixing
   - Added `getValidMediaPath()` method
   - Added `correctMediaPath()` method for domain correction

2. **`packages/inovector/mixpost-pro-team/src/SocialProviders/Twitter/TwitterProvider.php`**
   - Updated to use `ManagesResourcesFixed` instead of `ManagesResources`

## Verification Steps

After applying the fix:

1. **Test the specific failing post:**
   - Try posting the same content that was failing
   - Check Laravel logs for any new errors

2. **Check file paths:**
   ```bash
   php artisan tinker
   >>> use Inovector\Mixpost\Models\Media;
   >>> $media = Media::latest()->first();
   >>> echo $media->getFullPath();
   >>> echo file_exists($media->getFullPath()) ? 'EXISTS' : 'NOT FOUND';
   ```

3. **Monitor logs:**
   ```bash
   tail -f storage/logs/laravel.log
   ```

## Rollback Plan

If issues occur, revert the TwitterProvider:

```bash
git checkout packages/inovector/mixpost-pro-team/src/SocialProviders/Twitter/TwitterProvider.php
```

## Prevention

To prevent this issue in the future:

1. **Environment Consistency**: Ensure all environments use correct domain names
2. **Configuration Management**: Use proper deployment scripts that verify configuration
3. **Path Validation**: Consider adding path validation in media upload processes
4. **Monitoring**: Set up alerts for file not found errors

## Additional Notes

- The fix maintains backward compatibility
- Original functionality is preserved for working installations
- Enhanced logging helps with future debugging
- The solution handles both local and remote storage adapters

## Support

If you encounter issues:
1. Check the Laravel logs for detailed error messages
2. Run the diagnostic scripts to identify configuration problems
3. Verify file permissions and storage symlinks
4. Ensure the correct environment configuration is loaded
