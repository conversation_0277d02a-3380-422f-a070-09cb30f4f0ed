#!/bin/bash

echo "🔧 Fixing File Path Issue for GravityWrite Social Media Management"
echo "=================================================================="

# Step 1: Clear all Laravel caches
echo "📍 Step 1: Clearing Laravel caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
php artisan optimize:clear

# Step 2: Clear compiled class files
echo "📍 Step 2: Clearing compiled files..."
rm -f bootstrap/cache/config.php
rm -f bootstrap/cache/routes-*.php
rm -f bootstrap/cache/events.php
rm -f bootstrap/cache/packages.php
rm -f bootstrap/cache/services.php

# Step 3: Verify environment configuration
echo "📍 Step 3: Checking environment configuration..."
echo "Current APP_URL: $(php artisan tinker --execute='echo config("app.url");')"
echo "Current MIXPOST_DISK: $(php artisan tinker --execute='echo config("mixpost.disk");')"

# Step 4: Verify storage link
echo "📍 Step 4: Recreating storage link..."
php artisan storage:link --force

# Step 5: Test file path resolution
echo "📍 Step 5: Testing file path resolution..."
php artisan tinker --execute='
use Illuminate\Support\Facades\Storage;
$disk = config("mixpost.disk", "public");
echo "Disk: " . $disk . "\n";
echo "Root path: " . Storage::disk($disk)->path("") . "\n";
echo "Test path: " . Storage::disk($disk)->path("test.jpg") . "\n";
'

echo "✅ Cache clearing completed!"
echo "🔍 Please check the output above for any path mismatches."

# Step 6: Run debug script
echo "📍 Step 6: Running detailed path analysis..."
php debug-media-paths.php

# Step 7: Check for specific media file that's causing the issue
echo "📍 Step 7: Checking for problematic media files..."
php artisan tinker --execute='
use Inovector\Mixpost\Models\Media;
$media = Media::where("path", "like", "%8zibk6rGh6S0FgjjOkIPeU8FaM7wd8Nc5DJThZCW.jpg%")->first();
if ($media) {
    echo "Found problematic media:\n";
    echo "ID: " . $media->id . "\n";
    echo "Disk: " . $media->disk . "\n";
    echo "Path: " . $media->path . "\n";
    echo "Full Path: " . $media->getFullPath() . "\n";
    echo "File exists: " . (file_exists($media->getFullPath()) ? "Yes" : "No") . "\n";
} else {
    echo "Media file not found in database\n";
}
'

echo ""
echo "🎯 NEXT STEPS:"
echo "1. Check if the paths shown above contain 'clone-social.gravitywrite.com'"
echo "2. If yes, update your production .env file with correct APP_URL"
echo "3. Run this script again after updating .env"
echo "4. If paths are correct but files don't exist, check your storage symlink"
echo "5. Consider running the media path fix script if needed"
