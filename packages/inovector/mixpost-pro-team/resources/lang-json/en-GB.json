{"access_token": {"desc": " Access tokens allow third-party services to authenticate with our API on your behalf.", "last_usage": "Last usage", "expires_at": "Expires at", "no_result": "No Access Tokens added yet.", "delete": "Delete Access Token", "delete_confirm": "Are you sure you want to delete this token?", "never_used": "Never used", "delete_items": "Delete Access Tokens", "delete_items_confirm": "Are you sure you want to delete the selected tokens?", "access_tokens": "Access Tokens", "create": "Create token", "name_placeholder": "My cool token", "expiration": "Expiration", "never_expires": "Never expires", "copy_token": "Please copy this token now. You won't be able to see it again."}, "account": {"accounts": "Social Accounts", "account_updated": "The account has been updated", "account_reauthenticate": "The account cannot be updated. Re-authenticate your account.", "connect_social_account": "Connect a social account you'd like to manage.", "account_deleted": "Account deleted", "add_account": "Add account | Add accounts", "edit_suffix": "Edit suffix", "edit_account_suffix": "Edit account suffix", "enter_suffix": "Enter the suffix", "added": "Added:", "delete_account": "Delete account", "confirm_delete_account": "Are you sure you want to delete this account?", "account_not_updated": "The account cannot be updated.", "account_no_entities": "The account has no entities.", "choose_entity": "Choose entity", "account_entities": "Account Entities", "select_the_social_entities": "Select the social entities you want to connect", "connected": "Connected", "choose": "<PERSON><PERSON>", "access_token_expired": "Access token has expired. Reconnect your account.", "unauthorized": "Unauthorized", "add_social_account": "You don't have a social account, please add at least one.", "no_accounts_found": "No accounts found"}, "ai": {"tell_ai": "Tell AI what to write about", "write_about": "e.g. Write about a social media strategy for a small-medium business", "tone_": "<PERSON><PERSON>", "tone": {"neutral": "Neutral", "friendly": "Friendly", "formal": "Formal", "edgy": "<PERSON><PERSON>", "engaging": "Engaging"}, "rephrase": "Rephrase", "fix_spelling_grammar": "Fix Spelling and Grammar", "expand": "Expand", "shorten": "<PERSON>en", "simplify": "Simplify", "friendly_tone": "Change message tone to friendly", "formal_tone": "Use formal tone", "edgy_tone": "Use edgy tone", "engaging_tone": "Use engaging tone", "more_friendly": "More Friendly", "more_formal": "More Formal", "more_edgy": "More Edgy", "more_engaging": "More Engaging", "draft_content": "Draft content", "generated_content": "Generated content", "ai_assist": "AI Assist", "is_writing": "AI is writing", "ai_disabled": "AI is disabled! Please, configure an AI provider from services page and select it as default AI provider from settings page.", "ai": "AI", "ai_provider": "AI provider", "ai_services_not_configured": "You have not configured any AI services.", "configure_ai_service": "Configure an AI service", "instructions_assistant": "Instructions for assistant", "you_are_social": "e.g. You are a social media copywriter."}, "auth": {"login": "Log In", "login_account": "Log in to your account", "sign_in": "Sign In", "sign_out": "Sign Out", "let_email_address": "Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.", "enter_details": "Please enter your details", "remember_me": "Remember me", "dont_have_account": "Don't have an account?", "register_here": "Register here", "confirm_your_password": "Confirm your password", "confirm_forgot_password": "Forgot your password?", "update_password": "Update Password", "password_has_been_changed": "Password has been changed", "leave_blank_password": "Leave blank if you do not want to change the password", "confirm_secure_password": "Ensure your account is using a long, random password to stay secure.", "password_reset_link": "We have emailed your password reset link!", "send_password_reset": "Send password reset email", "password": "Password", "reset_password": "Reset password", "password_dont_match": "The current password field confirmation does not match.", "confirm_password": "Confirm Password", "two_factor_authentication": "Two Factor Authentication", "confirm_access_authentication_code": "Please confirm access to your account by entering the authentication code provided by your authenticator application.", "confirm_access_emergency_codes": "Please confirm access to your account by entering one of your emergency recovery codes.", "recovery_code": "Recovery code", "use_recovery_code": "Use a recovery code", "use_authentication_code": "Use an authentication code", "provided_two_factor_code_invalid": "The provided two factor code was invalid.", "security_using_two_factor": "Add additional security to your account using two factor authentication.", "two_factor_auth_code_invalid": "The provided two factor authentication code was invalid.", "forgot_password": "Forgot your password?"}, "calendar": {"calendar": "Calendar", "today": "Today", "month": "Month", "week": "Week", "year": "Year", "posts_on_date": "{0} No posts on {date}|{1} {count} post scheduled on {date}|[2,*] {count} posts scheduled on {date}", "weekdays": {"sunday": {"full": "Sunday", "short": "Sun", "shortest": "S"}, "monday": {"full": "Monday", "short": "Mon", "shortest": "M"}, "tuesday": {"full": "Tuesday", "short": "<PERSON><PERSON>", "shortest": "T"}, "wednesday": {"full": "Wednesday", "short": "Wed", "shortest": "W"}, "thursday": {"full": "Thursday", "short": "<PERSON>hu", "shortest": "T"}, "friday": {"full": "Friday", "short": "<PERSON><PERSON>", "shortest": "F"}, "saturday": {"full": "Saturday", "short": "Sat", "shortest": "S"}}, "months": {"january": {"full": "January", "short": "Jan"}, "february": {"full": "February", "short": "Feb"}, "march": {"full": "March", "short": "Mar"}, "april": {"full": "April", "short": "Apr"}, "may": {"full": "May", "short": "May"}, "june": {"full": "June", "short": "Jun"}, "july": {"full": "July", "short": "Jul"}, "august": {"full": "August", "short": "Aug"}, "september": {"full": "September", "short": "Sep"}, "october": {"full": "October", "short": "Oct"}, "november": {"full": "November", "short": "Nov"}, "december": {"full": "December", "short": "Dec"}}, "dayperiod": {"am": "AM", "pm": "PM"}, "days": "{count} days"}, "dashboard": {"dashboard": "Dashboard", "error_retrieving_analytics": "Error retrieving analytics.", "days": "days", "admin_console": "<PERSON><PERSON>", "enterprise_console": "Enterprise Console", "enterprise": "Enterprise"}, "editor": {"open_link_in": "Open link in", "current_window": "Current window", "new_window": "New window", "edit_link": "Edit Link", "align_left": "Align Left", "align_right": "Align Right", "align_center": "Align Center", "hard_break": "Hard break", "horizontal_line": "Horizontal line", "remove_image": "Remove Image", "add_reaction": "Add Reaction"}, "error": {"self_deletion_not_allowed": "You can't delete yourself", "browser_video_unsupported": "Your browser does not support the video tag.", "error_saving": "Error on saving", "try_again": "It's something wrong. Try again.", "something_wrong": "Something went wrong", "error_upload_video": "Error uploading the video. Try again later or try another video.", "request_timeout": "Request timeout. Try again.", "unknown_error": "Unknown error", "service_auth_failed": "Authentication failed! Please verify your {service} service credentials.", "page_expired": "The page expired, please try again."}, "finance": {"billing": "Billing"}, "general": {"add": "Add", "blocks": "Blocks", "button": "<PERSON><PERSON>", "browse": "Browse", "close": "Close", "cancel": "Cancel", "clean_up": "Clean up", "create": "Create", "created_at": "Created At", "color": "Color", "confirm": "Confirm", "code": "Code", "delete": "Delete", "default": "<PERSON><PERSON><PERSON>", "delete_permanently": "Delete permanently", "duplicate": "Duplicate", "detail": "Detail", "details": "Details", "detach": "<PERSON><PERSON>", "disable": "Disable", "disabled": "Disabled", "dismiss": "<PERSON><PERSON><PERSON>", "discard": "Discard", "done": "Done", "download": "Download", "edit": "Edit", "edit_role": "Edit role", "edit_link": "Edit Link", "email": "Email", "empty": "Empty", "enable": "Enable", "enabled": "Enabled", "filters": "Filters", "clear_filter": "Clear filter", "go_home": "Go home", "go_back": "Go Back", "hi": "Hi!", "insert": "Insert", "items": "items", "items_selected": "items selected", "input": "Input", "list_empty": "List is empty", "link": "Link", "name": "Name", "next": "Next", "no": "No", "open": "Open", "ok": "OK", "on": "On", "off": "Off", "rename": "<PERSON><PERSON>", "restore": "Rest<PERSON>", "remove": "Remove", "save": "Save", "save_changes": "Save changes", "search": "Search", "selected": "selected", "settings": "Settings", "status": "Status", "saved": "Saved", "textarea": "Textarea", "language": "Language", "timezone": "Timezone", "time": "Time", "title": "Title", "trash": "Trash", "update": "Update", "upload": "upload", "use": "Use", "view": "View", "yes": "Yes", "custom": "Custom", "active": "Active", "inactive": "Inactive", "security": "Security", "all": "All", "failed": "Failed", "succeeded": "Succeeded", "optional": "Optional", "generate": "Generate", "generate_new": "Generate new", "search_jump": "Search or jump to", "replace": "Replace", "retry": "Retry", "your_plan": "Your Plan", "is_required": "is required"}, "hashtag": {"hashtag_name": "Hashtag Group Name", "hashtag_content": "Hashtag Group Content", "dont_have_groups": "You don't have any groups yet", "open_hashtag": "Open Hashtag Manager", "hashtag_manager": "Hashtag Manager", "new_hashtag_group": "New Hashtag Group", "save_hashtag_group": "Save Hashtag Group"}, "installation": {"installation": "Installation", "create_admin": "Please create the initial administrator user.", "select_timezone": "Please, select your timezone."}, "media": {"media_library": "Media Library", "uploads": "Upload", "stock": "Stock Photos", "gifs": "GIFs", "library": "Library", "create_post": "Create Post", "delete_media": "Delete media", "do_you_want_delete": "Are you sure you want to delete selected media items?", "add_media": "Add Media", "downloading": "Downloading...", "no_gifs_found": "No GIFs found.", "click_configure": "Click to configure", "no_images_found": "No images found.", "drag_drop_files": "Drag & drop files here, or", "error_uploading_media": "Error uploading media.", "error_retrieving_media": "Error retrieving media. Try again!", "error_downloading_media": "Error downloading media. Try again!", "error_deleting_media": "Error deleting media. Try again!", "author": "Author", "image_source": "Image source", "aspect_ratio_range": "Aspect ratio must be between {min} and {max}.", "resolution_range": "Resolution must be minimum {min} x {max} px. Recommended is {recommended_min} x {recommended_max} px.", "duration_range": "Duration must be between {min} and {max} seconds.", "frame_rate_range": "Frame rate must be between {min} and {max} frames per second."}, "page": {"pages": "Pages", "page_desc": "Manage informative pages such as terms, privacy, about us...etc.", "delete_pages": "Delete pages", "confirm_delete_pages": "Are you sure you want to delete selected pages?", "pages_deleted": "Selected pages deleted", "create_page": "Create page", "no_pages_found": "No pages found.", "edit_page": "Edit page", "page_created": "Page has been created", "page_updated": "Page has been updated", "delete_page": "Delete page", "confirm_delete_page": "Are you sure you want to delete this page?", "page_deleted": "Page deleted", "container_width": "Container <PERSON><PERSON><PERSON>", "add_existing_block": "Add existing block", "no_blocks_created": "You have no blocks created.", "add_to_page": "Add to page", "module_not_found": "<PERSON><PERSON><PERSON> not found", "are_you_sure": "Are you sure?", "unsaved_will_lost": "Unsaved changes will be lost.", "block_created": "Block has been created", "save_page": "You have to save the page to make it visible.", "create_block": "Create Block", "create_add_to_page": "Create and add to page", "block_updated": "Block has been updated", "delete_block": "Delete block", "confirm_delete_block": "This block will be deleted from all pages and cannot be recovered.", "block_deleted": "Block deleted", "edit_block": "Edit Block", "search_blocks": "Type to search blocks", "sample_pages_generated": "Sample pages have been generated", "generate_samples": "Generate Samples", "generate_page_samples": "Generate page samples", "brand_logo": "Brand logo", "brand_name": "Brand name", "contact_email": "Contact email", "register_url": "Register url", "destroy_existing_pages": "Destroy existing pages and blocks", "remove_from_page": "Remove from this page", "page_no_blocks_added": "This page has no blocks added.", "generate": "Generate", "type_home": "Type <span class=\"bg-gray-200 px-1\">home</span> for the home page", "meta_title": "Meta Title", "meta_description": "Meta Description", "medium": "Medium", "small": "Small", "url_path": "URL Path"}, "post": {"posts": "Posts", "posts_deleted": "Selected posts deleted permanently", "posts_to_trash": "Selected posts moved to Trash", "drafts": "Drafts", "scheduled": "Scheduled", "published": "Published", "failed": "Failed", "status": "Status", "content": "Content", "media": "Media", "labels": "Labels", "accounts": "Accounts", "author": "Author", "no_posts_found": "No posts found.", "delete_posts": "Delete posts", "your_post": "Your post", "confirmation_delete_post": "Are you sure you want to delete selected posts?", "posts_history_not_edited": "Posts in history cannot be edited.", "post_being_published": "This post is being published, check back shortly!", "posts_in_trash_not_be_edited": "Posts in trash cannot be edited.", "view_in_calendar": "View in calendar", "pick_time": "Pick time", "clear_time": "Clear time", "schedule": "Schedule", "post_now": "Post now", "add_to_queue": "Add to Queue", "confirm_publication": "Confirm publication", "now_confirm_publication": "This post will be immediately published to the following social accounts. Are you sure?", "no_simultaneous_post": "{provider} does not allow simultaneous posting of identical content to multiple accounts.", "post_deleted_permanently": "Post deleted permanently", "post_moved_to_trash": "Post moved to Trash", "post_duplicated": "Post duplicated", "post_restored": "Post restored", "delete_post": "Delete post", "confirm_delete_post": "Are you sure you want to delete this post?", "rules": {"no_mixed_media": "Combining different media types (videos, photos, GIFs) is not allowed.", "max_char": "Maximum of {count} characters allowed.", "max_photos": "Maximum of {count} photo allowed. | Maximum of {count} photos allowed.", "max_videos": "Maximum of {count} video allowed. | Maximum of {count} videos allowed.", "max_gifs": "Maximum of {count} GIF allowed. | Maximum of {count} GIFs allowed.", "min_images": "A minimum of ${count} image is required. | A minimum of ${count} images is required.", "min_char": "Content length should be at least {count} character. | Content length should be at least {count} characters long."}, "view_media": "View Media", "no_media_selected": "No images or videos are selected for this post.", "video_not_selected": "No video is selected for this post.", "select_account": "Select an account and start writing your post in the left panel to start.", "original": "Original", "create_version": "Create version", "create_version_for": "Create version for", "remove_version": "Remove version", "confirmation_delete_version": "Are you sure you would like to delete version for {account_name} [{account_provider}]?", "post_scheduled": "The post has been scheduled.", "add_image": "Add Image", "post_scheduled_timezone": "Post will be scheduled according to this timezone. Click to update it.", "past_selected_date": "The selected date and time is in the past", "past_date": "The date is in the past.", "configuration": "Configuration", "post_cannot_scheduled": "This post cannot be scheduled!", "accounts_not_selected": "No accounts selected.", "posting_schedule_not_available_times": "The posting schedule has no available times.", "start_write": "Start writing...", "preview": "Preview", "draft": "Draft", "publishing": "Publishing", "video_processing_notice": "After publishing, it may take a few minutes for the video to process and be visible on your profile.", "post": "Post", "no_first_comment": "The first comment will not be published on {providers}.", "add_first_comment": "Add first comment", "add_post": "Add Post", "first_comment": "First Comment", "only_one_first_comment": "The first comment is limited to one item, the rest will be ignored.", "approval_required": "Post must be approved before it can be published.", "needs_approval": "Needs approval", "approve": "Approve", "view": "View Post"}, "post_activity": {"activity": "Activity", "write_comment": "Write a comment...", "send": "Send", "reply_n": "{count} reply | {count} replies", "reply": "Reply", "thread_by": "Thread by", "delete_comment": "Delete comment", "delete_comment_confirm": "Are you sure you want to delete this comment?", "new_comment": "New comment", "by": "By {name}", "new_activity": "New activity", "new_post_activity": "New activity for post", "created_post": "{user} created this post", "post_publishing_failed": "Publishing process failed", "post_published": "Post successfully published", "schedule_approval": "{ user } scheduled this post for {datetime} and submitted it for approval", "post_approved": "{user} approved scheduling this post for {datetime}", "scheduled_post": "{user} scheduled this post for {datetime}", "publishing": "Publishing process initiated", "user_set_drafts": "{user} moved this post to drafts", "set_drafts": "The post has been moved to drafts.", "update_scheduled_at": "{user} updated the schedule time from {old_datetime} to {new_datetime}", "watch": "Watch", "notify_all_activity": "Notify me on all activity for this task.", "unwatch": "Unwatch", "notify_mentions_only": "Notify me only on mentions."}, "posting_schedule": {"everyday": "Everyday", "add_post_time": "Add Posting Time", "posting_schedule": "Posting Schedule", "posting_times_updated": "Posting times has been updated successfully", "send_posting_schedule": "Your posting schedule tells the system when to send posts to the queue.", "add_new_posting_time": "Add new posting time", "posting_times": "Posting times", "clear_all_posting_times": "Clear all Posting Times", "update_posting_times": "Update posting times", "confirmation_posting_times": "Are you sure you want to clear all posting times?", "posting_times_determined": "Posting times will be determined according to this timezone. Click to update it."}, "profile": {"edit_profile": "Edit Profile", "profile_information": "Profile Information", "update_your_account": "Update your account's profile information and email address.", "preferences": "My Preferences", "update_preferences": "Update your general preferences.", "security_confirm_password": "For your security, please confirm your password to continue.", "password_changed": "Password have been changed", "current_password": "Current password", "new_password": "New password", "confirm_new_password": "Confirm new password", "preferences_updated": "Preferences have been updated", "time_format": "Time format", "hour": "hour", "first_day_week": "First day of week", "sunday": "Sunday", "monday": "Monday", "two_factor_enabled": "Two factor authentication enabled", "two_factor_disabled": "Two factor authentication disabled", "install_totp": "Install a TOTP compatible mobile authentication application such as Google Authenticator on your mobile device or computer.", "scan_qr_code": "Scan this QR code with your authentication app, fill the code and click Confirm.", "secret_key": "Your secret key", "store_recovery_codes": "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.", "regenerate_recovery_codes": "Regenerate recovery codes", "show_recovery_codes": "Show recovery codes", "code": "Code", "delete_account": "Delete Account", "delete_account_desc": "Deleting your account will result in the permanent removal of all account data and associated resources, including the data of any owned workspaces."}, "report": {"audience": "Audience", "followers_per_day": "The number of followers per day during the selected period", "posts_liked": "The number of times where your posts were liked", "likes": "<PERSON>s", "comments": "Comments", "impressions": "Impressions", "impressions_posts": "The number of times people saw your posts", "followers": "Followers", "members": "Members"}, "service": {"third_party_services": "Third Party Services", "third_party_services_desc": "This page is for storing the credentials for third party services.", "create_app": "Create an App on {name}", "configure_services_desc": "Click on the button below to configure the third-party services.", "configure_services": "Configure services", "representative_data": "These data are not real, only representative.", "not_configured_service": "You have not configured {service} service.", "service_saved": "{service} service has been saved", "tenor": {"search_gifs": "Search Tenor GIFs", "gif": "GIF from Tenor", "author": "Author", "use_gif": "With Tenor you can use GIF's directly in Mixpost."}, "unsplash": {"search": "Search Unsplash", "can_use_external_photos": "With Unsplash you can use external stock photos directly in Mixpost."}, "meta": {"reel_supports_one_video": "Reels only supports one video", "reel_only_video_allowed": "Reel only supports video.", "error": {"session_expired": "Session expired. Try posting again.", "media_already_published": "The media has already been published.", "publication_video_expired": "Publication video expired", "required_param_missing": "Required parameter missing in API call."}, "post": "Post", "reel": "<PERSON><PERSON>", "story_single_media_limit": "Story only supports one image or video", "story": "Story"}, "facebook": {"connect_group": "Connect a new Facebook group", "connect_page": "Connect a new Facebook page", "report": {"number_members_per_day": "The number of members per day during the selected period", "number_times_posts_impressions": "The number of times your Page's posts entered a person's screen. Posts include statuses, photos, links, videos and more.", "page_engaged_users": "Page Engaged Users", "post_engagements": "Post Engagements", "posts_impressions": "Posts Impressions", "number_people_page": "The number of people who engaged with your Page. Engagement includes any click.", "number_times_post_engagements": "The number of times people have engaged with your posts through reactions, comments, shares and more."}, "page_options": "Facebook page options"}, "instagram": {"connect_account": "Connect a new Business Instagram account", "report": {"number_comments_posts": "The number of comments on your posts", "engagement_report": "Sum of email contacts, phone calls, direction clicks, text message clicks and website clicks", "engagement": "Engagement", "clicks_divided_impressions": "(Email contacts + Phone call clicks + Get direction clicks + Text message clicks + Website clicks) divided by Impressions", "engagement_rate": "Engagement Rate", "number_taps_email": "Total number of taps on the email link in the profile", "email_contacts": "Email contacts", "number_new_followers": "Total number of new followers", "follower_count": "Follower count", "number_directions_clicks": "Total number of taps on the directions link in the profile", "directions_clicks": "Directions clicks", "number_phone_call_clicks": "Total number of taps on the call link in the profile", "phone_call_clicks": "Phone call clicks", "number_profile_views": "Total number of users who have viewed the profile", "profile_views": "Profile views", "number_reach": "Total number of unique users who have viewed at least one of the media item", "reach": "Reach", "number_text_message_clicks": "Total number of taps on the text message link in the profile", "text_message_clicks": "Text message clicks", "number_website_clicks": "Total number of taps on the website link in the profile", "website_clicks": "Website clicks"}, "select_video_image": "Select a video or image for this Instagram account."}, "linkedin": {"connect_page": "Connect a new LinkedIn page", "connect_profile": "Connect a new LinkedIn profile", "visibility": "Visibility", "public": "Public", "connections": "Connections", "not_support_accounts": "Unfortunately, LinkedIn's API does not support showing analytics for personal LinkedIn accounts.", "insights": "Insights", "job_title": "Your job title", "send": "Send"}, "tiktok": {"supports_only_videos": "TikTok only supports video files.", "connect_profile": "Connect a new TikTok profile", "disclose": "Content Disclosure Setting", "your_brand": "Your brand", "branded_content": "Branded content", "who_watch_video": "Who can watch this video", "direct_share_type": "TikTok videos are sent to the inbox for editing and publishing from the TikTok application. The text will not be sent.", "for_you": "For You", "select_video": "Select a video for this TikTok account.", "everyone": "Everyone", "friends": "Friends", "only_you": "Only you", "followers": "Followers", "report": {"views_video": "The number of times your videos have been viewed", "views": "Views", "number_shares": "The number of times your videos have been shares", "shares": "Shares", "number_comments": "The total number of comments"}, "video_limit": "Only one video is allowed per post.", "disclose_desc": "Turn on to disclose that this video promotes goods or services in exchange for something of value. Your video could promote yourself, a third party, or both.", "promo_video_alert": "Your video will be labeled \"Promotional content\". ", "branded_content_desc": "You are promoting another brand or a third party. This video will be classified as Branded Content.", "your_brand_desc": "You are promoting yourself or your own business. This video will be classified as Brand Organic.", "your_brand_accept_terms": "By posting, you agree to TikTok's <a href=\"{href}\" target=\"_blank\" class=\"link\">Music Usage Confirmation</a>.", "accept_terms": "By posting, you agree to TikTok's <a href=\"{href_brand}\" target=\"_blank\" class=\"link\">Branded Content Policy</a> and <a href=\"{href_music}\" target=\"_blank\" class=\"link\">Music Usage Confirmation</a>.", "partner_video_alert": "Your video will be labeled \"Paid partnership\". ", "visibility_branded_content": "Visibility for branded content can't be private. Select \"Everyone/Friends\" visibility to enable this option.", "allow_users": "Allow users to", "comment": "Comment", "duet": "Duet", "stitch": "St<PERSON>", "branded_no_private": "Branded content visibility cannot be set to private.", "content_disclosure_required": "You need to indicate if your content promotes yourself, a third party, or both.", "privacy_level_required": "It looks like you haven't chosen who can watch your video."}, "pinterest": {"not_support_video": "Pinterest is temporarily not supporting video uploads.", "video_upload_failed": "Error uploading the video. Try again later or try another video.", "connect_profile": "Connect a new Pinterest profile", "board_created": "The board has been created successfully", "board_not_added": "The board could not be added", "board_name": "Board name", "create_board": "Create Pinterest Board", "save_rate": "Save Rate", "not_account": "This is not a Pinterest account", "select_board": "Select a board", "report": {"number_pins_saved": "The number of times your pins have been saved", "number_pin_clicks": "The total number of clicks on your Pin or ad so it opens in closeup.", "pin_clicks": "Pin clicks", "number_impressions": "The total saves of your Pins divided by the total number of times your Pins were on screen.", "number_outbound_clicks": "The number of times people perform actions that lead them to a destination off Pinterest.", "outbound_clicks": "Outbound clicks"}, "select_board_for": "Select a board for <span class=\"font-semibold mr-1\">{account}</span>"}, "twitter": {"connect_profile": "Connect a new X profile", "reports_limited": "You use X's free API. Reports may be limited.", "upgrade": "Upgrade X API Tier", "number_retweets": "The number of times your tweets have been retweeted", "retweets": "Retweets", "upload_failed": "Failed to upload file", "form": {"edit_app": "You will need to edit the App Permissions and allow \"Read and Write\""}}, "youtube": {"connect_profile": "Connect a new YouTube profile", "public": "Public", "private": "Private", "unlisted": "Unlisted", "analytics_under_development": "YouTube analytics is under development.", "select_video": "Select a video for this YouTube account.", "privacy_config": "Privacy configuration", "title": "Video or short title"}, "mastodon": {"upload_failed": "Media could not be uploaded to the server.", "connect_profile": "Connect a new Mastodon profile", "enter_server": "Enter your Mastodon server", "mark_media": "Mark media as sensitive", "report": {"number_replies": "The number of replies to your posts", "replies": "Replies", "number_reblogs": "The number of times your posts have been reblogs", "reblogs": "Reblogs", "favourites_number": "The number of times your posts have been added to favorites", "favourites": "Favourites"}}, "provider_options": "{provider} options", "services": "Services", "threads": {"connect": "Connect a new Threads profile"}}, "subscription": {"upgrade_required": "Upgrade Required", "plan_limit_reached_message": "You have reached your plan limit or your subscription has expired. Please upgrade your plan to continue using this feature.", "upgrade_plan": "Upgrade Plan"}, "system": {"system": "System", "log_file_cleared": "Log file cleared successfully!", "system_logs": "System Logs", "logs": "Logs", "there_are_no_logs": "There are no logs.", "status": "Status", "system_status": "System Status", "describe_your_issue": "Describe your issue", "info_copied": "System status information copied to clipboard", "error_copy_info": "Error copying status information", "health": "Health", "environment": "Environment:", "debug_mode": "Debug Mode", "queue_connection": "Queue connection", "no_queue_connection": "No valid <span class=\"font-medium\">queue connection</span> found.", "schedule": "Schedule", "technical_details": "Technical details", "app_directory": "App directory", "upload_media_disk": "Upload Media Disk", "log_channel": "Log Channel", "user_agent": "User agent", "connection_settings_redis_exist": "Queue connection settings for mixpost-redis exist.", "never_started": "It never started", "config_connection": "Configure a queue connection with the <span class=\"font-medium\">mixpost-redis</span> key.", "copy": "Copy", "active": "Active", "inactive": "Inactive", "paused": "paused", "not_ok": "Not OK", "ran_time_ago": "Ran {time} minute(s) ago", "copied_clipboard": "Copied to clipboard!", "failed_copied_clipboard": "Failed to copy to clipboard!", "usage_api": "Usage in API", "stored_safely": "I've stored it safely"}, "tag": {"change_label_color": "Change label color", "change_color": "Change color", "delete_label": "Delete label", "label_deleted": "Label deleted", "confirm_label_delete": "Are you sure you want to delete the {name} label from everywhere?", "create_new_label": "Press Enter or Save to create a new label", "search_create_post": "Search or Create New", "no_labels_found": "No labels found"}, "team": {"add_user": "Add user", "team": "Team", "add_user_workspace": "Add new user to this workspace", "edit_role_for": "Edit role for", "change_role": "Change role", "can_edit_everything": "Can access and edit everything.", "edit_exceptions": "Can access and edit everything except configuration section.", "user_attached": "User attached", "attach_user": "Attach user", "user_detached": "User detached", "role_updated": "Role updated", "role": "Role", "edit_role": "Edit role", "edit_role_on": " Edit role on", "attach": "Attach", "attached_at": "Attached At", "detach_workspace": "Detach workspace", "attach_workspace": "Attach workspace", "detach_confirm": "Are you sure you want to detach <strong>{workspace}</strong> from <strong>{user}</strong>?", "admin": "Admin", "member": "Member", "can_approve": "Can Approve", "viewer": "Viewer", "viewer_access": "Can access everything except configuration section, but cannot edit, schedule posts."}, "template": {"templates": "Templates", "create_template": "Create Template", "do_not_have_templates": "You don't have any templates yet", "edit_template": "Edit template", "create_new_template": "Create new template", "template_created": "The template was created successfully", "template_updated": "The template has been successfully updated", "error_saving_template": "Error saving the template", "use_template": "Use template", "create_new": "Create new", "template_name": "Template name", "save_template": "Save template", "save_create_new": "Save and create new", "save_content_current_version": "You will save the content of the current version of the post.", "open_template_manager": "Open Template Manager", "template_manager": "Template manager", "save_as_template": "Save As Template", "templates_desc": "Create posts quickly using predefined templates."}, "theme": {"customization": "Customization", "change_logo_favicon": "Change the logo and favicon of your company.", "change_theme_color": "Change theme color", "upload_logo": "Upload logo", "remove_favicon": "Remove favicon", "upload_favicon": "Upload favicon", "theme_color": "Theme color", "rebrand_company": "Rebrand your company"}, "user": {"create_user": "Create user", "delete_users": "Delete users", "select_user": "Please select a user", "confirm_delete_users": "Are you sure you want to delete selected users?", "selected_users_deleted": "Selected users deleted", "manage_users": "Manage users", "confirm_delete_user": "Are you sure you want to delete this user?", "user_deleted": "User deleted", "view_user": "View user", "user_has_been_updated": "User has been updated", "delete_user": "Delete user", "type_search_user": "Type to search user", "system_admin": "System Admin", "users": "Users", "user_details": "User Details", "user_created": "User created"}, "util": {"read_doc": "Read the <a href=\"{href}\" target=\"_blank\" class=\"link\">documentation</a>.", "impersonating": "Impersonating", "no_results": "There are no results.", "coming_soon": "Coming soon..."}, "variable": {"save_variable": "Save Variable", "open_variable_manager": "Open Variable Manager", "variable_manager": "Variable manager", "new_variable": "New Variable", "dont_have_variables": "You don't have any variables yet", "name_account": "The name of the account that the post is published to.", "platform_post": "The platform on which the post is published, e.g. Facebook...", "value": "Value"}, "vendor": {"color_picker": "Color Picker", "chart": {"drag_chart": "You can drag the chart to zoom.", "reset_zoom": "Reset zoom"}}, "webhook": {"edit_webhook": "<PERSON>", "callback_url": "Callback URL", "method": "Method", "max_attempts": "Max attempts", "secret": "Secret", "events": "Events", "webhooks": "Webhooks", "create_webhook": "Create webhook", "deliveries": "Webhook deliveries", "never_triggered": "Never triggered", "last_delivery_succeeded": "The last delivery was successful.", "last_delivery_failed": "Last delivery failed.", "delete_webhook": "Delete webhook", "delete_webhook_confirm": "Are you sure you want to delete this webhook?", "webhooks_desc": "Allow external services to be notified when certain events happen.", "event": {"post": {"created": "Post Created", "updated": "Post Updated", "deleted": "Post Deleted", "scheduled": "Post Scheduled", "published": "Post Published", "publishing_failed": "Publishing Post Failed"}, "account": {"added": "Account Added", "updated": "Account Updated", "deleted": "Account Deleted"}}, "change_secret": "Change secret", "change_secret_desc": " If you lose or forget this secret, you can change it, but remember to update any integrations using it.", "secret_updated": "Secret successfully updated", "delete_webhooks": "Delete webhooks", "delete_webhooks_confirm": "Are you sure you want to delete selected webhooks?", "resend": "Resend", "next_retry": "Next retry", "resend_manually": "This webhook was resent manually", "response": "Response", "http_status": "HTTP Status code", "payload": "Payload", "resent": "Webhook has been resent", "updated": "Webhook updated successfully", "not_found": "Webhook not found", "deleted": "Webhook deleted successfully", "created": "Webhook created successfully", "delete_webhooks_failed": "Webhooks could not be deleted", "delete_webhooks_success": "Webhooks deleted successfully", "delivery_failed_try_redeliver": "Delivery of this webhook failed. The system will try to redeliver the event later.", "delivery_failed": "Delivery of this webhook failed.", "content_type": "Content-Type"}, "workspace": {"edit_workspace": "Edit workspace", "workspace_updated": "Workspace has been updated", "delete_workspace": "Delete workspace", "confirm_delete_workspace": "Are you sure you want to delete this workspace?", "workspace_deleted": "Workspace deleted", "workspace_name": "Workspace name", "create_and_login": "Create and Log In", "change_workspace_color": "Change workspace color", "view_workspace": "View workspace", "workspaces": "Workspaces", "create_workspace": "Create workspace", "delete_workspaces": "Delete workspaces", "confirm_delete_workspaces": "Are you sure you want to delete selected workspaces?", "workspaces_deleted": "Selected workspaces deleted", "manage_brands_businesses": "Manage several brands and businesses.", "type_search_workspace": "Type to search workspace"}}