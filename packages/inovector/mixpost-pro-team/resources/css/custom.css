@layer components {
    .aside {
        @apply w-[240px];
    }

    .main {
        width: calc(theme('width.full') - 240px);
    }

    .calendar-month-height {
        height: calc(theme('height.screen') - 139px);
    }

    .calendar-week-height-sm {
        height: calc(theme('height.screen') - 181px);
    }

    .calendar-week-height-md {
        height: calc(theme('height.screen') - 131px);
    }

    .calendar-week-height {
        height: calc(theme('height.screen') - 90px);
    }

    .calendar-year-height {
        height: calc(theme('height.screen') - 139px);
    }

    .form-field {
        @apply max-w-xl;
    }

    .link {
        @apply text-blue-500 hover:text-blue-400 transition-colors ease-in-out duration-200;
    }

    .link-lighter {
        @apply text-blue-300 hover:text-blue-200 transition-colors ease-in-out duration-200;
    }

    .link-primary {
        @apply font-medium text-primary-500 hover:text-primary-700;
    }

    .destructive {
        @apply text-red-500 hover:text-red-400 transition-colors ease-in-out duration-200;
    }

    .pt-4rem {
        padding-top: 4rem;
    }
}

@layer utilities {
    .row-px {
        @apply px-3 sm:px-6 lg:px-8;
    }

    .row-py {
        @apply py-3 sm:py-6 lg:py-8;
    }

    .row-mb {
        @apply mb-3 sm:mb-6 lg:mb-8;
    }

    .row-pb {
        @apply pb-3 sm:pb-6 lg:pb-8;
    }

    .row-mt {
        @apply mt-3 sm:mt-6 lg:mb-8;
    }

    .row-pt {
        @apply pt-3 sm:pt-6 lg:pt-8;
    }

    .m-container {
        @apply max-w-6xl;
    }

    .s-container {
        @apply max-w-3xl;
    }

    .mixpost-scroll-style {
        scrollbar-width: thin;
    }

    .mixpost-scroll-style::-webkit-scrollbar {
        @apply invisible w-3;
    }

    .mixpost-scroll-style::-webkit-scrollbar-track {
        @apply bg-gray-50 rounded;
    }

    .mixpost-scroll-style::-webkit-scrollbar-thumb {
        @apply bg-gray-300 rounded-md border-4 border-solid border-white transition-colors ease-in-out duration-200;
    }

    .mixpost-scroll-style::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-400;
    }

    .group-visible {
        @apply opacity-0 group-hover:opacity-100 transition-opacity ease-in-out duration-200;
    }
}
