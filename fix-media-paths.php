<?php

/**
 * Fix media paths that reference incorrect domain
 * Run this with: php fix-media-paths.php
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Inovector\Mixpost\Models\Media;

echo "🔧 GravityWrite Social Media Path Fixer\n";
echo "======================================\n\n";

$oldDomain = 'clone-social.gravitywrite.com';
$newDomain = 'social.gravitywrite.com';

echo "Looking for media records with incorrect paths...\n";

// Check if we're dealing with URL paths or file paths
$mediaWithIncorrectPaths = Media::where('path', 'like', "%{$oldDomain}%")->get();

if ($mediaWithIncorrectPaths->count() > 0) {
    echo "Found {$mediaWithIncorrectPaths->count()} media records with incorrect paths:\n\n";
    
    foreach ($mediaWithIncorrectPaths as $media) {
        echo "Media ID: {$media->id}\n";
        echo "  Current Path: {$media->path}\n";
        
        $newPath = str_replace($oldDomain, $newDomain, $media->path);
        echo "  New Path: {$newPath}\n";
        
        // Ask for confirmation before updating
        echo "  Update this record? (y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) === 'y') {
            $media->path = $newPath;
            $media->save();
            echo "  ✅ Updated!\n";
        } else {
            echo "  ⏭️ Skipped\n";
        }
        echo "\n";
    }
} else {
    echo "✅ No media records found with incorrect domain paths.\n";
}

// Check for conversion paths as well
echo "\nChecking for incorrect conversion paths...\n";

$mediaWithIncorrectConversions = Media::whereRaw("JSON_EXTRACT(conversions, '$[*].path') LIKE ?", ["%{$oldDomain}%"])->get();

if ($mediaWithIncorrectConversions->count() > 0) {
    echo "Found {$mediaWithIncorrectConversions->count()} media records with incorrect conversion paths:\n\n";
    
    foreach ($mediaWithIncorrectConversions as $media) {
        echo "Media ID: {$media->id}\n";
        echo "  Current Conversions: " . json_encode($media->conversions) . "\n";
        
        $conversions = $media->conversions;
        $updated = false;
        
        foreach ($conversions as &$conversion) {
            if (isset($conversion['path']) && strpos($conversion['path'], $oldDomain) !== false) {
                $conversion['path'] = str_replace($oldDomain, $newDomain, $conversion['path']);
                $updated = true;
            }
        }
        
        if ($updated) {
            echo "  New Conversions: " . json_encode($conversions) . "\n";
            
            echo "  Update this record? (y/N): ";
            $handle = fopen("php://stdin", "r");
            $line = fgets($handle);
            fclose($handle);
            
            if (trim(strtolower($line)) === 'y') {
                $media->conversions = $conversions;
                $media->save();
                echo "  ✅ Updated!\n";
            } else {
                echo "  ⏭️ Skipped\n";
            }
        }
        echo "\n";
    }
} else {
    echo "✅ No media records found with incorrect conversion paths.\n";
}

echo "\n🎯 Path fixing completed!\n";
echo "💡 Remember to:\n";
echo "1. Clear caches after making changes\n";
echo "2. Verify that the actual files exist at the new paths\n";
echo "3. Test posting functionality\n";
